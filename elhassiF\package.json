{"name": "free-nextjs-admin-dashboard", "version": "2.0.2", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "postinstall": "prisma generate"}, "dependencies": {"@fullcalendar/core": "^6.1.15", "@fullcalendar/daygrid": "^6.1.15", "@fullcalendar/interaction": "^6.1.15", "@fullcalendar/list": "^6.1.15", "@fullcalendar/react": "^6.1.15", "@fullcalendar/timegrid": "^6.1.15", "@headlessui/react": "^2.2.2", "@prisma/client": "^6.8.2", "@radix-ui/react-slot": "^1.2.0", "@react-jvectormap/core": "^1.0.4", "@react-jvectormap/world": "^1.1.2", "@tailwindcss/forms": "^0.5.9", "@tailwindcss/postcss": "^4.0.9", "@tanstack/react-query": "^5.74.11", "@types/bcrypt": "^5.0.2", "@types/lodash": "^4.17.17", "@upstash/redis": "^1.34.8", "apexcharts": "^4.3.0", "bcrypt": "^5.1.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^3.6.0", "flatpickr": "^4.6.13", "formidable": "^3.5.2", "framer-motion": "^12.9.2", "jsonwebtoken": "^9.0.2", "jwt-decode": "^4.0.0", "lodash": "^4.17.21", "lucide-react": "^0.487.0", "multer": "^1.4.5-lts.2", "next": "^15.3.0", "prisma": "^6.8.2", "react": "^19.1.0", "react-apexcharts": "^1.7.0", "react-day-picker": "^9.6.6", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^19.1.0", "react-dropzone": "^14.3.5", "react-icons": "^5.5.0", "react-intersection-observer": "^9.16.0", "react-toastify": "^11.0.5", "swiper": "^11.2.0", "tailwind-merge": "^2.6.0", "ts-node": "^10.9.2", "tw-animate-css": "^1.2.5", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@svgr/webpack": "^8.1.0", "@types/formidable": "^3.4.5", "@types/jsonwebtoken": "^9.0.9", "@types/multer": "^1.4.12", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-transition-group": "^4.4.12", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.1.3", "postcss": "^8.5.3", "tailwindcss": "^4.1.3", "typescript": "^5"}, "overrides": {"@react-jvectormap/core": {"react": "^16.8.0 || ^17 || ^18 || ^19", "react-dom": "^16.8.0 || ^17 || ^18 || ^19"}, "@react-jvectormap/world": {"react": "^16.8.0 || ^17 || ^18 || ^19", "react-dom": "^16.8.0 || ^17 || ^18 || ^19"}}, "prisma": {"seed": "ts-node --compiler-options {\"module\":\"CommonJS\"} prisma/seed.ts"}}