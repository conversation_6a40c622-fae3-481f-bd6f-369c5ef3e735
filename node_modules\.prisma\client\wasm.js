
/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */

Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('./runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.8.2
 * Query Engine version: 2060c79ba17c6bb9f5823312b6f6b7f4a845738e
 */
Prisma.prismaVersion = {
  client: "6.8.2",
  engine: "2060c79ba17c6bb9f5823312b6f6b7f4a845738e"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  nom: 'nom',
  prenom: 'prenom',
  email: 'email',
  mot_de_passe: 'mot_de_passe',
  role: 'role',
  photo: 'photo',
  cree_le: 'cree_le'
};

exports.Prisma.MedecinScalarFieldEnum = {
  id: 'id',
  specialite: 'specialite',
  telephone: 'telephone',
  userId: 'userId'
};

exports.Prisma.PatientScalarFieldEnum = {
  id: 'id',
  numero_patient: 'numero_patient',
  nom: 'nom',
  prenom: 'prenom',
  telephone: 'telephone',
  date_naissance: 'date_naissance',
  genre: 'genre',
  photo: 'photo',
  consultation: 'consultation',
  consultation_specialisee: 'consultation_specialisee',
  ct_sim: 'ct_sim',
  debut_traitement: 'debut_traitement',
  fin_traitement: 'fin_traitement',
  rdv_traitement: 'rdv_traitement',
  technique_irradiation: 'technique_irradiation',
  dose_totale: 'dose_totale',
  dose_fraction: 'dose_fraction',
  cree_le: 'cree_le',
  adresse: 'adresse',
  antecedent: 'antecedent',
  diagnostic: 'diagnostic',
  medecinId: 'medecinId'
};

exports.Prisma.FileAttenteScalarFieldEnum = {
  id: 'id',
  statut: 'statut',
  ordre: 'ordre',
  ajoute_le: 'ajoute_le',
  medecinId: 'medecinId',
  patientId: 'patientId'
};

exports.Prisma.ConsultationScalarFieldEnum = {
  id: 'id',
  patientId: 'patientId',
  medecinId: 'medecinId',
  date: 'date',
  diagnostic: 'diagnostic',
  traitement: 'traitement',
  notes: 'notes',
  type: 'type',
  cree_le: 'cree_le',
  debut: 'debut',
  fin: 'fin'
};

exports.Prisma.MessageScalarFieldEnum = {
  id: 'id',
  contenu: 'contenu',
  date: 'date',
  medecinId: 'medecinId',
  patientId: 'patientId',
  type: 'type'
};

exports.Prisma.HistoriqueConsultationScalarFieldEnum = {
  id: 'id',
  action: 'action',
  date: 'date',
  medecinId: 'medecinId',
  patientId: 'patientId'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};
exports.Role = exports.$Enums.Role = {
  admin: 'admin',
  medecin: 'medecin',
  super_admin: 'super_admin'
};

exports.Genre = exports.$Enums.Genre = {
  Homme: 'Homme',
  Femme: 'Femme',
  Autre: 'Autre'
};

exports.StatutFileAttente = exports.$Enums.StatutFileAttente = {
  EN_ATTENTE: 'EN_ATTENTE',
  EN_COURS: 'EN_COURS'
};

exports.TypeConsultation = exports.$Enums.TypeConsultation = {
  INITIAL: 'INITIAL',
  STANDARD: 'STANDARD',
  SPECIALISEE: 'SPECIALISEE'
};

exports.ActionHistorique = exports.$Enums.ActionHistorique = {
  AJOUT_FILE: 'AJOUT_FILE',
  CONSULTATION: 'CONSULTATION',
  FIN_TRAITEMENT: 'FIN_TRAITEMENT',
  AUTRE: 'AUTRE'
};

exports.Prisma.ModelName = {
  User: 'User',
  Medecin: 'Medecin',
  Patient: 'Patient',
  FileAttente: 'FileAttente',
  Consultation: 'Consultation',
  Message: 'Message',
  HistoriqueConsultation: 'HistoriqueConsultation'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }

        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
